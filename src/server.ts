// Fastify 服务器配置
import { log } from 'crawlee';
import Fastify from 'fastify';
import * as cron from 'node-cron';
import { crawlerManager, runCrawler } from './crawler.js';
import { registerApiRoutes } from './api.js';
import { dongcheyundianDataSync } from './fetch-schedule/dongcheyundian/index.js';
import { cheyundianToyotaShortVideoDataSyncHandler } from './fetch-schedule/cheyundian-toyota-short-video/index.js';
import { cheyundianToyoTaBIDataSyncHandler } from './fetch-schedule/cheyundian-toyota-bi/index.js';
import { cheyundianToyotaPerformanceDataSyncHandler } from './fetch-schedule/cheyundian-toyota-performance/index.js';

// 创建 Fastify 实例
const fastify = Fastify({
  logger: {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
      },
    },
  },
});

// 注册所有 API 接口
registerApiRoutes(fastify);

/**
 * 设置定时任务
 */
function setupCronJob() {
  const timezone = 'Asia/Shanghai';

  // 每天 10 11 12 点执行
  const crawlerCronExpression = '0 0 10,11,12 * * *';
  // 测试
  // const crawlerCronExpression = '*/15 * * * *';
  cron.schedule(
    crawlerCronExpression,
    async () => {
      try {
        log.info('定时任务触发：开始执行爬虫任务');
        // 检查是否已有爬虫在运行
        if (crawlerManager.isRunning()) {
          log.error('定时任务跳过：爬虫正在运行中');
          return;
        }
        await runCrawler();
        log.info('定时任务完成：爬虫执行成功');
      } catch (error) {
        log.error('定时任务失败：爬虫执行出错', { error });
      }
    },
    {
      timezone,
    }
  );

  // 每日 10 12 执行懂车云店数据同步任务
  const dongcheyundianCronExpression = '0 0 10,12 * * *';
  cron.schedule(
    dongcheyundianCronExpression,
    async () => {
      try {
        log.info('定时任务触发：开始执行懂车云店数据同步任务');
        await dongcheyundianDataSync();
        log.info('定时任务完成：懂车云店数据同步执行成功');
      } catch (error) {
        log.error('定时任务失败：懂车云店数据同步执行出错', { error });
      }
    },
    {
      timezone,
    }
  );

  // 每日 9-12点执行车云店丰田短视频数据同步任务
  const cheyundianToyotaShortVideoCronExpression = '0 0 9,10,11,12 * * *';
  cron.schedule(
    cheyundianToyotaShortVideoCronExpression,
    async () => {
      try {
        log.info('定时任务触发：开始执行车云店丰田短视频数据同步任务');
        await cheyundianToyotaShortVideoDataSyncHandler();
        log.info('定时任务完成：车云店丰田短视频数据同步执行成功');
      } catch (error) {
        log.error('定时任务失败：车云店丰田短视频数据同步执行出错', { error });
      }
    },
    {
      timezone,
    }
  );

  // 每日 9、10、11、12、14点执行车云店丰田BI数据同步任务
  const cheyundianToyotaBICronExpression = '0 0 9,10,11,12,14 * * *';
  cron.schedule(
    cheyundianToyotaBICronExpression,
    async () => {
      try {
        log.info('定时任务触发：开始执行车云店丰田BI数据同步任务');
        await cheyundianToyoTaBIDataSyncHandler();
        log.info('定时任务完成：车云店丰田BI数据同步执行成功');
      } catch (error) {
        log.error('定时任务失败：车云店丰田BI数据同步执行出错', { error });
      }
    },
    {
      timezone,
    }
  );

  // 每日 10、11、12点执行车云店丰田业绩数据同步任务
  const cheyundianToyotaPerformanceCronExpression = '0 0 10,11,12 * * *';
  cron.schedule(
    cheyundianToyotaPerformanceCronExpression,
    async () => {
      try {
        log.info('定时任务触发：开始执行车云店丰田业绩数据同步任务');
        await cheyundianToyotaPerformanceDataSyncHandler();
        log.info('定时任务完成：车云店丰田业绩数据同步执行成功');
      } catch (error) {
        log.error('定时任务失败：车云店丰田业绩数据同步执行出错', { error });
      }
    },
    {
      timezone,
    }
  );
}

/**
 * 启动服务器
 */
export async function startServer(port: number = 3000) {
  try {
    log.info(`正在启动服务器，端口: ${port}`);

    // 设置定时任务
    setupCronJob();

    // 启动服务器
    await fastify.listen({ port, host: '0.0.0.0' });
    log.info(`服务器已启动，监听端口: ${port}`);
    log.info(`健康检查端点: http://0.0.0.0:${port}/health`);
    log.info(`爬虫状态端点: http://0.0.0.0:${port}/api/crawler/status`);

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string) => {
      log.info(`收到 ${signal} 信号，开始优雅关闭...`);
      try {
        await fastify.close();
        log.info('服务器已优雅关闭');
        process.exit(0);
      } catch (error) {
        log.error('关闭服务器时出错:', { error });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  } catch (error) {
    log.error('服务器启动失败:', { error });
    process.exit(1);
  }
}
